# 🤖 AGENTE ATUALIZADOR DE PERMISSÕES

## 🎯 COMANDO DE ATIVAÇÃO
Para ativar este agente, use uma das seguintes frases:
- "Atualizar permissões"
- "Atualizar mapeamento de ações"
- "Regenerar planilhas de permissões"
- "Sincronizar ações da pasta Funcionalidades"

## 📋 PROMPT COMPLETO DO AGENTE

```
🔄 ATUALIZADOR DE PERMISSÕES ATIVADO

Preciso atualizar as planilhas Excel e página HTML com as ações e permissões após realizar alterações nos arquivos da pasta Funcionalidades.

Por favor:

1. **Analise todos os arquivos** na pasta `src/constants/Funcionalidades/` e extraia:
   - Todas as ações definidas (chaves dos objetos exportados)
   - Os tipos de usuário que têm acesso a cada ação (valores dos arrays EnumTipoUsuario)
   - Os nomes das constantes exportadas de cada arquivo

2. **Verifique o enum** em `src/constants/Enum/enumTipoUsuario.ts` para confirmar os mapeamentos dos tipos de usuário

3. **Atualize os seguintes arquivos** com os dados mais recentes:
   - `Mapeamento_Acoes_Funcionalidades_com_Constantes.xlsx` (planilha Excel principal)
   - `Mapeamento_Acoes_Funcionalidades_com_Constantes.csv` (arquivo CSV)
   - `Mapeamento_Acoes_Funcionalidades_com_Constantes.html` (página web interativa)

4. **Mantenha o formato** com nomes das constantes:
   - Exemplo: `CadastroRevendaAcao.CADASTRAR_REVENDAS`
   - Exemplo: `AssinaturaAcao.ASSINATURA_EXIBIR_BANCO_DADOS`

5. **Gere estatísticas atualizadas** por tipo de usuário

6. **Execute os scripts** para verificar se tudo está funcionando:
   - Script Node.js: `node gerar_excel.cjs`
   - Script Python: `python gerar_planilha_acoes.py`

**Colunas da planilha:**
Ação, SISTEMA_ADMIN, SISTEMA_FINANCEIRO, DESENVOLVEDOR, REVENDA_ADMIN, REVENDA_ASSISTENTE, SUPORTE_STI3, CANAIS_GERENTE, ANALISTA_CONTEUDO

**Formato de saída:** SIM/NÃO para cada permissão, ordenado alfabeticamente por ação.

**Arquivos de referência:**
- Pasta de origem: `src/constants/Funcionalidades/`
- Enum de usuários: `src/constants/Enum/enumTipoUsuario.ts`
- Scripts disponíveis: `gerar_excel.cjs`, `gerar_planilha_acoes.py`, `gerar_csv_com_constantes.cjs`
```

## 🔧 CONFIGURAÇÃO DO AGENTE

### Contexto:
- **Workspace:** `src/ClientApp`
- **Arquivos monitorados:** `src/constants/Funcionalidades/*.ts`
- **Outputs:** Excel, CSV, HTML
- **Scripts:** Node.js e Python

### Triggers:
- Mudanças em arquivos .ts da pasta Funcionalidades
- Comando manual do usuário
- Adição de novos arquivos de ação

### Ações automáticas:
1. Scan da pasta Funcionalidades
2. Extração de ações e permissões
3. Atualização dos arquivos de saída
4. Validação dos scripts
5. Geração de relatório de mudanças

## 📊 TEMPLATE DE RESPOSTA

Quando ativado, o agente deve seguir este template:

```
✅ AGENTE ATUALIZADOR DE PERMISSÕES EXECUTADO

📊 Análise realizada:
- Total de arquivos analisados: [X]
- Total de ações encontradas: [X]
- Novas ações detectadas: [X]
- Ações removidas: [X]
- Permissões modificadas: [X]

📁 Arquivos atualizados:
✅ Mapeamento_Acoes_Funcionalidades_com_Constantes.xlsx
✅ Mapeamento_Acoes_Funcionalidades_com_Constantes.csv  
✅ Mapeamento_Acoes_Funcionalidades_com_Constantes.html

🔧 Scripts executados:
✅ node gerar_excel.cjs
✅ python gerar_planilha_acoes.py
✅ node gerar_csv_com_constantes.cjs

📈 Estatísticas atualizadas:
- SISTEMA_ADMIN: [X] permissões
- DESENVOLVEDOR: [X] permissões
- CANAIS_GERENTE: [X] permissões
- [etc...]

🎯 Próximos passos:
1. Verificar os arquivos gerados
2. Testar a página HTML
3. Validar as permissões no Excel
```

## 🚀 COMO USAR

### Ativação Manual:
1. Digite: "Atualizar permissões"
2. Adicione contexto específico se necessário
3. Aguarde a execução completa

### Exemplo de uso:
```
Atualizar permissões

Alterações realizadas:
- Adicionei 3 novas ações no arquivo cadastro_produto.ts
- Modifiquei permissões de REVENDA_ADMIN no assinatura.ts
- Criei novo arquivo relatorio.ts com 5 ações
```

## 📝 HISTÓRICO DE EXECUÇÕES

| Data | Alterações | Status | Observações |
|------|------------|--------|-------------|
| [DATA] | [MUDANÇAS] | ✅ | [NOTAS] |

---
**Última atualização:** [DATA]
**Versão do agente:** 1.0
**Criado por:** Augment Agent
