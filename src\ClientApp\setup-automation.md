# 🚀 CONFIGURAÇÃO DA AUTOMAÇÃO DE PERMISSÕES

## 📋 RESUMO DAS IMPLEMENTAÇÕES

### ✅ **OPÇÃO 1: Agente Augment**
- **Arquivo:** `AGENTE_ATUALIZACAO_PERMISSOES.md`
- **Comando:** "Atualizar permissões"
- **Funcionalidade:** Prompt inteligente com memória

### ✅ **OPÇÃO 3: GitHub Actions**
- **Arquivo:** `.github/workflows/update-permissions.yml`
- **Triggers:** Manual, Push, Pull Request
- **Funcionalidade:** Automação completa CI/CD

## 🤖 COMO USAR O AGENTE AUGMENT

### Ativação:
```
Atualizar permissões

Alterações realizadas:
- [Descreva suas mudanças aqui]
```

### Comandos alternativos:
- "Atualizar mapeamento de ações"
- "Regenerar planilhas de permissões"
- "Sincronizar ações da pasta Funcionalidades"

## ⚙️ COMO CONFIGURAR GITHUB ACTIONS

### 1. Mover o arquivo para o local correto:
```bash
# Criar a estrutura de pastas no repositório raiz
mkdir -p .github/workflows

# Mover o arquivo
mv src/ClientApp/.github/workflows/update-permissions.yml .github/workflows/
```

### 2. Configurar permissões no GitHub:
1. Vá para **Settings** > **Actions** > **General**
2. Em **Workflow permissions**, selecione:
   - ✅ "Read and write permissions"
   - ✅ "Allow GitHub Actions to create and approve pull requests"

### 3. Testar a execução manual:
1. Vá para **Actions** no seu repositório
2. Selecione "🔄 Atualizar Mapeamento de Permissões"
3. Clique em **Run workflow**
4. Preencha a descrição das alterações
5. Execute

## 🔄 TRIGGERS AUTOMÁTICOS

### O GitHub Actions será executado automaticamente quando:

1. **Push para main/develop** com alterações em:
   - `src/constants/Funcionalidades/**/*.ts`
   - `src/constants/Enum/enumTipoUsuario.ts`

2. **Pull Requests** que modificam os mesmos arquivos

3. **Execução manual** via interface do GitHub

## 📊 OUTPUTS GERADOS

### Arquivos atualizados automaticamente:
- ✅ `Mapeamento_Acoes_Funcionalidades_com_Constantes.xlsx`
- ✅ `Mapeamento_Acoes_Funcionalidades_com_Constantes.csv`
- ✅ `Mapeamento_Acoes_Funcionalidades_com_Constantes.html`

### Artifacts disponíveis:
- 📦 Download dos arquivos gerados
- 📈 Relatório de estatísticas
- 📋 Log completo da execução

## 🛠️ COMANDOS LOCAIS

### Para testar localmente:
```bash
# Navegar para a pasta
cd src/ClientApp

# Executar scripts Node.js
node gerar_excel.cjs
node gerar_csv_com_constantes.cjs

# Executar script Python
python gerar_planilha_acoes.py
```

## 🔧 TROUBLESHOOTING

### Problemas comuns:

1. **Erro de permissões no GitHub:**
   - Verificar configurações em Settings > Actions

2. **Dependências não instaladas:**
   - O workflow instala automaticamente
   - Para local: `npm install xlsx` e `pip install pandas openpyxl`

3. **Arquivos não encontrados:**
   - Verificar estrutura de pastas
   - Confirmar que os scripts estão na pasta correta

## 📈 MONITORAMENTO

### Como acompanhar:
1. **GitHub Actions tab** - Status das execuções
2. **Commits automáticos** - Mudanças detectadas
3. **Artifacts** - Downloads dos arquivos gerados
4. **Summary** - Relatório de cada execução

## 🎯 PRÓXIMOS PASSOS

### Após configurar:
1. ✅ Testar execução manual no GitHub
2. ✅ Fazer uma alteração em arquivo de Funcionalidades
3. ✅ Verificar execução automática
4. ✅ Validar arquivos gerados
5. ✅ Testar agente Augment

### Melhorias futuras:
- 🔔 Notificações por email/Slack
- 📊 Dashboard de permissões
- 🔄 Integração com sistemas externos
- 📝 Versionamento automático

## 📞 SUPORTE

### Em caso de problemas:
1. Verificar logs no GitHub Actions
2. Testar scripts localmente
3. Usar o agente Augment como backup
4. Consultar documentação do GitHub Actions

---
**Criado em:** $(date)
**Versão:** 1.0
**Autor:** Augment Agent
